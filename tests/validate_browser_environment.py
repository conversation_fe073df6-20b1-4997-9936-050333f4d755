#!/usr/bin/env python3
"""
Browser Control Environment Validation Script
Comprehensive validation of browser control environment setup
"""

import sys
import os
import subprocess
import socket
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from python.helpers.browser_control_environment import BrowserEnvironment


class EnvironmentValidator:
    """Validates browser control environment setup"""
    
    def __init__(self):
        self.results = {
            "overall_status": "unknown",
            "checks": [],
            "errors": [],
            "warnings": []
        }
    
    def run_all_validations(self):
        """Run comprehensive environment validation"""
        print("🔍 Browser Control Environment Validation")
        print("=" * 50)
        
        # System requirements checks
        self.check_system_requirements()
        
        # Docker environment checks
        self.check_docker_environment()
        
        # Process management checks  
        self.check_process_management()
        
        # Integration readiness checks
        self.check_integration_readiness()
        
        # Finalize results
        self.finalize_results()
        
        # Print summary
        self.print_summary()
        
        return self.results['overall_status'] == "passed"
    
    def check_system_requirements(self):
        """Check basic system requirements"""
        print("\n📋 Checking System Requirements...")
        
        # Check Xvfb
        if self._check_command_available("Xvfb"):
            self._add_result("Xvfb availability", True, "Xvfb is installed and accessible")
        else:
            self._add_result("Xvfb availability", False, "Xvfb is not installed or not in PATH")
        
        # Check Chrome/Chromium
        chrome_found = False
        for browser in ['google-chrome', 'chromium', 'chromium-browser']:
            if self._check_command_available(browser):
                chrome_found = True
                self._add_result(f"{browser} availability", True, f"{browser} is installed and accessible")
                break
        
        if not chrome_found:
            self._add_result("Chrome/Chromium availability", False, "No Chrome or Chromium browser found")
        
        # Check X11 utilities
        x11_utils = ['xdpyinfo', 'x11vnc']
        for util in x11_utils:
            if self._check_command_available(util):
                self._add_result(f"{util} availability", True, f"{util} is available")
            else:
                self._add_result(f"{util} availability", False, f"{util} is not installed", warning=True)
    
    def check_docker_environment(self):
        """Check Docker-specific environment setup"""
        print("\n🐳 Checking Docker Environment...")
        
        # Check if running in Docker
        is_docker = os.path.exists('/.dockerenv')
        self._add_result("Docker environment detection", is_docker, 
                        "Running in Docker" if is_docker else "Not running in Docker")
        
        if is_docker:
            # Check Docker-specific requirements
            self._check_docker_display_setup()
            self._check_docker_permissions()
        else:
            print("  ℹ️  Skipping Docker-specific checks (not in container)")
    
    def check_process_management(self):
        """Check process management functionality"""
        print("\n⚙️  Checking Process Management...")
        
        # Test environment manager instantiation
        try:
            env = BrowserEnvironment(display=":98", debug_port=9223)  # Use test values
            self._add_result("Environment manager instantiation", True, 
                           "BrowserEnvironment class can be instantiated")
            
            # Test configuration
            self._add_result("Environment configuration", 
                           env.display == ":98" and env.debug_port == 9223,
                           "Environment manager accepts configuration parameters")
            
            # Test health check method
            health = env.health_check()
            self._add_result("Health check functionality", isinstance(health, dict),
                           "Health check method returns expected format")
            
        except Exception as e:
            self._add_result("Environment manager functionality", False, 
                           f"Failed to instantiate or test environment manager: {str(e)}")
    
    def check_integration_readiness(self):
        """Check readiness for browser-use integration"""
        print("\n🔗 Checking Integration Readiness...")
        
        # Check browser-use compatibility
        self._check_browser_use_compatibility()
        
        # Check port availability  
        self._check_port_availability(9222, "Chrome remote debugging")
        self._check_port_availability(5900, "VNC server")
        
        # Check file system permissions
        self._check_filesystem_permissions()
    
    def _check_command_available(self, command: str) -> bool:
        """Check if a command is available in PATH"""
        try:
            result = subprocess.run(['which', command], capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False
    
    def _check_docker_display_setup(self):
        """Check Docker display setup"""
        # Check for X11 socket directory
        x11_dir = Path("/tmp/.X11-unix")
        if x11_dir.exists():
            self._add_result("X11 socket directory", True, "X11 socket directory exists")
        else:
            self._add_result("X11 socket directory", False, "X11 socket directory missing", warning=True)
        
        # Check for display environment
        display_env = os.environ.get('DISPLAY')
        if display_env:
            self._add_result("DISPLAY environment variable", True, f"DISPLAY set to {display_env}")
        else:
            self._add_result("DISPLAY environment variable", False, "DISPLAY environment variable not set", warning=True)
    
    def _check_docker_permissions(self):
        """Check Docker container permissions"""
        # Check if running as root (required for Xvfb in Docker)
        is_root = os.geteuid() == 0
        self._add_result("Root permissions", is_root, 
                        "Running as root (required for Xvfb)" if is_root else "Not running as root")
        
        # Check tmp directory permissions
        tmp_writable = os.access('/tmp', os.W_OK)
        self._add_result("Temporary directory writable", tmp_writable,
                        "/tmp directory is writable" if tmp_writable else "/tmp directory not writable")
    
    def _check_browser_use_compatibility(self):
        """Check browser-use library compatibility"""
        try:
            # Try importing browser-use
            from python.helpers.browser_use import browser_use
            self._add_result("browser-use import", True, "browser-use library is available")
            
            # Check if BrowserSession supports external connections
            if hasattr(browser_use.BrowserSession, '__init__'):
                self._add_result("BrowserSession compatibility", True, 
                               "BrowserSession class is available for external connections")
            else:
                self._add_result("BrowserSession compatibility", False, 
                               "BrowserSession class not found")
                
        except Exception as e:
            self._add_result("browser-use compatibility", False, 
                           f"browser-use library not available: {str(e)}")
    
    def _check_port_availability(self, port: int, description: str):
        """Check if a port is available"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('localhost', port))
            sock.close()
            self._add_result(f"Port {port} availability", True, 
                           f"Port {port} ({description}) is available")
        except OSError:
            self._add_result(f"Port {port} availability", False, 
                           f"Port {port} ({description}) is already in use", warning=True)
    
    def _check_filesystem_permissions(self):
        """Check filesystem permissions for browser control"""
        test_dirs = ['/tmp/browser_profile', '/tmp/chrome_test']
        
        for test_dir in test_dirs:
            try:
                Path(test_dir).mkdir(parents=True, exist_ok=True)
                # Test write permissions
                test_file = Path(test_dir) / 'test_write'
                test_file.write_text('test')
                test_file.unlink()  # Clean up
                
                self._add_result(f"Directory {test_dir} permissions", True, 
                               f"Can create and write to {test_dir}")
                
                # Clean up test directory
                Path(test_dir).rmdir()
                
            except Exception as e:
                self._add_result(f"Directory {test_dir} permissions", False,
                               f"Cannot write to {test_dir}: {str(e)}")
    
    def _add_result(self, check_name: str, passed: bool, message: str, warning: bool = False):
        """Add a validation result"""
        result = {
            "check": check_name,
            "passed": passed,
            "message": message,
            "warning": warning
        }
        
        self.results["checks"].append(result)
        
        # Print immediate result
        icon = "✅" if passed else ("⚠️" if warning else "❌")
        print(f"  {icon} {check_name}: {message}")
        
        if not passed and not warning:
            self.results["errors"].append(message)
        elif warning:
            self.results["warnings"].append(message)
    
    def finalize_results(self):
        """Finalize validation results"""
        total_checks = len(self.results["checks"])
        passed_checks = len([c for c in self.results["checks"] if c["passed"]])
        error_count = len(self.results["errors"])
        
        if error_count == 0:
            self.results["overall_status"] = "passed"
        elif error_count <= 2:
            self.results["overall_status"] = "passed_with_warnings"
        else:
            self.results["overall_status"] = "failed"
        
        self.results["summary"] = {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": total_checks - passed_checks,
            "error_count": error_count,
            "warning_count": len(self.results["warnings"])
        }
    
    def print_summary(self):
        """Print validation summary"""
        summary = self.results["summary"]
        status = self.results["overall_status"]
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        status_icons = {
            "passed": "🟢 PASSED",
            "passed_with_warnings": "🟡 PASSED WITH WARNINGS", 
            "failed": "🔴 FAILED"
        }
        
        print(f"Overall Status: {status_icons.get(status, '❓ UNKNOWN')}")
        print(f"Total Checks: {summary['total_checks']}")
        print(f"Passed: {summary['passed_checks']}")
        print(f"Failed: {summary['failed_checks']}")
        print(f"Errors: {summary['error_count']}")
        print(f"Warnings: {summary['warning_count']}")
        
        if self.results["errors"]:
            print(f"\n❌ Critical Issues:")
            for error in self.results["errors"]:
                print(f"  • {error}")
        
        if self.results["warnings"]:
            print(f"\n⚠️  Warnings:")
            for warning in self.results["warnings"]:
                print(f"  • {warning}")
        
        if status == "passed":
            print(f"\n🎉 Environment validation passed! Ready for browser control PoC implementation.")
        elif status == "passed_with_warnings":
            print(f"\n✅ Environment validation passed with warnings. PoC should work but may have limitations.")
        else:
            print(f"\n❌ Environment validation failed. Address critical issues before proceeding.")


def main():
    """Run environment validation"""
    validator = EnvironmentValidator()
    success = validator.run_all_validations()
    
    # Save results to file
    results_file = Path(__file__).parent / "validation_results.json"
    with open(results_file, 'w') as f:
        json.dump(validator.results, f, indent=2)
    print(f"\n💾 Results saved to: {results_file}")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())