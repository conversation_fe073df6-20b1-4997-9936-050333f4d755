{"overall_status": "failed", "checks": [{"check": "Xvfb availability", "passed": false, "message": "Xvfb is not installed or not in PATH", "warning": false}, {"check": "Chrome/Chromium availability", "passed": false, "message": "No Chrome or Chromium browser found", "warning": false}, {"check": "xdpyinfo availability", "passed": false, "message": "xdpyinfo is not installed", "warning": true}, {"check": "x11vnc availability", "passed": false, "message": "x11vnc is not installed", "warning": true}, {"check": "Docker environment detection", "passed": false, "message": "Not running in <PERSON><PERSON>", "warning": false}, {"check": "Environment manager instantiation", "passed": true, "message": "BrowserEnvironment class can be instantiated", "warning": false}, {"check": "Environment configuration", "passed": true, "message": "Environment manager accepts configuration parameters", "warning": false}, {"check": "Health check functionality", "passed": true, "message": "Health check method returns expected format", "warning": false}, {"check": "browser-use import", "passed": true, "message": "browser-use library is available", "warning": false}, {"check": "BrowserSession compatibility", "passed": true, "message": "BrowserSession class is available for external connections", "warning": false}, {"check": "Port 9222 availability", "passed": true, "message": "Port 9222 (Chrome remote debugging) is available", "warning": false}, {"check": "Port 5900 availability", "passed": true, "message": "Port 5900 (VNC server) is available", "warning": false}, {"check": "Directory /tmp/browser_profile permissions", "passed": true, "message": "Can create and write to /tmp/browser_profile", "warning": false}, {"check": "Directory /tmp/chrome_test permissions", "passed": true, "message": "Can create and write to /tmp/chrome_test", "warning": false}], "errors": ["Xvfb is not installed or not in PATH", "No Chrome or Chromium browser found", "Not running in <PERSON><PERSON>"], "warnings": ["xdpyinfo is not installed", "x11vnc is not installed"], "summary": {"total_checks": 14, "passed_checks": 9, "failed_checks": 5, "error_count": 3, "warning_count": 2}}